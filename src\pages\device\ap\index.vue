<script lang="ts" setup>
import type { UploadFile, UploadFiles, UploadRawFile } from 'element-plus'
import { ElMessage, ElMessageBox, ElUpload, genFileId } from 'element-plus'
import { computed, reactive, ref, watch, watchEffect } from 'vue'
import { useI18n } from 'vue-i18n'
import {
  BAND_WIDTH_2G,
  COUNTRY_OPTIONS,
  NET_TYPE,
  PROTOCOL_2G,
  TX_POWER_2G,
} from '@/utils/constants'

const { t } = useI18n()

// 侧边栏
const drawer = ref(false)
const deviceInfoTable: any = ref([])
const selectedBandwidth = ref('20M')
const HTMode = ref('HT20')

const selectedRows = ref<any[]>([])

const itemsPerPage = ref(10)
const page = ref(1)

// 表头
const headers = [
  { title: t('Device.AP.Name'), key: 'name', sortable: false },
  { title: t('Device.AP.Model'), key: 'model', sortable: false },
  { title: t('Device.AP.SerialNumber'), key: 'sn', sortable: true },
  { title: t('Device.AP.IPAddress'), key: 'ip', sortable: false },
  { title: t('Device.AP.MACAddress'), key: 'mac', sortable: false },
  { title: t('Device.AP.FirmwareVersion'), key: 'version', sortable: false },
  { title: t('Device.AP.RunningTime'), key: 'uptime', sortable: false },
  { title: t('Device.AP.Status'), key: 'onoff', sortable: false },
  { title: t('Device.AP.Actions'), key: 'actions', sortable: false },
]

// 子网掩码选项列表
const subnetMaskOptions = [
  // A类网络常用
  { label: '*********/8', value: '*********' },
  { label: '***********/9', value: '***********' },
  { label: '***********/10', value: '***********' },
  { label: '***********/11', value: '***********' },
  { label: '***********/12', value: '***********' },
  { label: '***********/13', value: '***********' },
  { label: '***********/14', value: '***********' },
  { label: '***********/15', value: '***********' },

  // B类网络常用
  { label: '***********/16', value: '***********' },
  { label: '*************/17', value: '*************' },
  { label: '*************/18', value: '*************' },
  { label: '255.255.224.0/19', value: '255.255.224.0' },
  { label: '255.255.240.0/20', value: '255.255.240.0' },
  { label: '255.255.248.0/21', value: '255.255.248.0' },
  { label: '255.255.252.0/22', value: '255.255.252.0' },
  { label: '255.255.254.0/23', value: '255.255.254.0' },

  // C类网络常用
  { label: '255.255.255.0/24', value: '255.255.255.0' },
  { label: '255.255.255.128/25', value: '255.255.255.128' },
  { label: '255.255.255.192/26', value: '255.255.255.192' },
  { label: '255.255.255.224/27', value: '255.255.255.224' },
  { label: '255.255.255.240/28', value: '255.255.255.240' },
  { label: '255.255.255.248/29', value: '255.255.255.248' },
  { label: '255.255.255.252/30', value: '255.255.255.252' },

  // 特殊用途
  { label: '255.255.255.254/31', value: '255.255.255.254' },
  { label: '255.255.255.255/32', value: '255.255.255.255' },
]

const operation = ref()

// No longer reset selections when operation changes
const operationChange = () => {
  // Keep selections intact
}

const apHandlers = ref([{
  label: t('Device.AP.Operations.Restart'),
  value: 0,
}, {
  label: t('Device.AP.Operations.FactoryReset'),
  value: 1,
}, {
  label: t('Device.AP.Operations.Upgrade'),
  value: 2,
}, {
  label: t('Device.AP.Operations.Delete'),
  value: 3,
}, {
  label: t('Device.AP.Operations.BlinkLED'),
  value: 4,
}, {
  label: t('Device.AP.Operations.ExportSN'),
  value: 5,
}])

// 数据
const apData = ref({
  total: 0,
  apList: [] as any[],
})

const selectedModel = ref<string | null>(null)
const modelList = ref([] as { label: string; value?: string }[])

// 新增：获取型号列表
const getModelList = () => {
  $get('/v1/apModel', {}).then(res => {
    if (res.msg === 'success' && Array.isArray(res.result)) {
      modelList.value = res.result.map((item: any) => ({
        label: item || '',
        value: item || '',
      }))
      modelList.value.unshift({
        label: t('Device.AP.All'),
        value: '',
      })

      // 初始不选中任何型号
      selectedModel.value = null
    }
  })
}

const ledMode = ref('1')

const resetAfterUpdate = ref('0')

const uploader = ref()
const fileList = ref()

const handleChange = (uploadFile: UploadFile) => {
  fileList.value = [uploadFile]
}

const handleExceed = (files: UploadFiles) => {
  uploader.value!.clearFiles()

  const file = files[0] as UploadRawFile

  file.uid = genFileId()
  uploader.value!.handleStart(file)
}

// 排序相关变量
const sortBy = ref<{ key: string; order?: string }[]>([])

// 服务器端分页，不再需要本地排序和过滤

// 扫描
const activeAP: any = ref({})

const router = useRouter()

const remoteConfigFun = (item: any) => {
  if (item.net_type == 0) {
    // 打开新页面
    const url = `http://${item.ip}`
    const windowName = '_blank'

    window.open(url, windowName)
  }
  else {
    router.push({ name: 'device-remote', query: { id: item.id } })
  }
}

const deviceManagerFun = (item: any) => {
  console.log('deviceManagerFun', item)

  $post('', {
    requestType: 506,
  }).then(res => {
    if (res.msg === 'success')
      dealData(item, res.info.aplist)
  })
}

// 2.4G
const wirelessFormItems: any = ref([])

// 5G
const wirelessFormItems5G: any = ref([])

// 5.8G 备用
const wirelessFormItems8G: any = ref([])

console.log(wirelessFormItems5G.value, wirelessFormItems8G.value)

const workModeFormItems: any = ref([])

const nameModeFormItems: any = ref([])

// 监听国家码变化并更新信道列表
const channelList = computed(() => {
  if (!wirelessFormItems.value?.[6]?.value)
    return []

  // 根据国家码获取对应的信道列表
  const countryCode = wirelessFormItems.value[6]?.value
  const index = COUNTRY_OPTIONS.findIndex((data: any) => data.value === countryCode)
  const result = CHANNEL_ARR_2G[index] || []

  return result.map(item => ({
    label: item,
    value: item,
  }))
})

const channelList5G = computed(() => {
  if (!wirelessFormItems8G.value?.[6]?.value)
    return []

  // 根据国家码获取对应的信道列表
  const countryCode = wirelessFormItems8G.value[6]?.value
  const index = COUNTRY_OPTIONS.findIndex((data: any) => data.value === countryCode)
  const result = CHANNEL_ARR_5G[index] || []

  return result.map(item => ({
    label: item,
    value: item,
  }))
})

const isFirstUpdate = ref(true)
const isFirstUpdate8G = ref(true)

onMounted(() => {
  watchEffect(() => {
    if (wirelessFormItems.value?.[6]) {
      // 创建一个新的对象来触发响应式更新
      const updatedItem = reactive({
        ...wirelessFormItems.value[7],

        // 只在当前没有有效值或不在新列表中时才设为auto
        ...(!wirelessFormItems.value[7].value
           || !channelList.value.some(item => item.value === wirelessFormItems.value[7].value)
          ? { value: 'auto' }
          : {}),
        list: channelList.value,
      })

      // 确保信道列表正确更新
      wirelessFormItems.value[7] = updatedItem
      isFirstUpdate.value = false
    }
  })
  watchEffect(() => {
    if (wirelessFormItems8G.value?.[6]) {
      // 创建一个新的对象来触发响应式更新
      const updatedItem = reactive({
        ...wirelessFormItems8G.value[7],

        // 只在当前没有有效值或不在新列表中时才设为auto
        ...(!wirelessFormItems8G.value[7].value
           || !channelList5G.value.some(item => item.value === wirelessFormItems8G.value[7].value)
          ? { value: 'auto' }
          : {}),
        list: channelList5G.value,
      })

      // 确保信道列表正确更新
      wirelessFormItems8G.value[7] = updatedItem
      isFirstUpdate8G.value = false
    }
  })
})

const COUNTRY_OPTIONS_LOCALIZED = computed(() => {
  return COUNTRY_OPTIONS.map(item => ({
    ...item,
    label: item.label, // 无需翻译，因为COUNTRY_OPTIONS已经是国家代码
  }))
})

const NET_TYPE_LOCALIZED = computed(() => {
  return NET_TYPE.map(item => ({
    ...item,
    label: t(item.label),
  }))
})

const TX_POWER_2G_LOCALIZED = computed(() => {
  return TX_POWER_2G.map(item => ({
    ...item,
    label: t(item.label),
  }))
})

const TX_POWER_5G_LOCALIZED = computed(() => {
  return TX_POWER_5G.map(item => ({
    ...item,
    label: t(item.label),
  }))
})

const WIFI_ENCRYPTION_TYPE_LOCALIZED = computed(() => {
  return ENCRYPTION_TYPE.map(item => ({
    ...item,
    label: item.label, // 无需翻译，因为WIFI_ENCRYPTION_TYPE已经是技术名称
  }))
})

const PROTOCOL_2G_LOCALIZED = computed(() => {
  return PROTOCOL_2G.map(item => ({
    ...item,
    label: item.label, // 无需翻译，因为PROTOCOL_2G已经是技术标准名称
  }))
})

const BAND_WIDTH_2G_LOCALIZED = computed(() => {
  return BAND_WIDTH_2G.map(item => ({
    ...item,
    label: item.label, // 无需翻译，因为BAND_WIDTH_2G已经是技术参数名称
  }))
})

// IP地址验证器 - 验证最后一位在1-254之间
const validateLanIp = (value: string) => {
  if (!value)
    return true // 允许空值，如果需要必填可以在另一个验证器中处理

  // IP地址格式验证
  const ipRegex = /^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$/
  const match = value.match(ipRegex)

  if (!match)
    return t('Device.AP.InvalidIPFormat')

  // 检查每个部分是否在0-255之间
  const parts = []
  for (let i = 1; i <= 4; i++) {
    const part = Number.parseInt(match[i], 10)
    if (part < 0 || part > 255)
      return t('Device.AP.IPRangeError')
    parts.push(part)
  }

  // 验证是否为私有网络IP地址范围 (RFC 1918)
  const [p1, p2, , p4] = parts
  const isPrivateA = p1 === 10 // 10.0.0.0 - **************
  const isPrivateB = p1 === 172 && (p2 >= 16 && p2 <= 31) // ********** - **************
  const isPrivateC = p1 === 192 && p2 === 168 // *********** - ***************

  if (!(isPrivateA || isPrivateB || isPrivateC))
    return t('Device.AP.NotPrivateIP')

  // 验证最后一位不能为0或255（网络地址和广播地址）
  if (p4 === 0 || p4 === 255)
    return t('Device.AP.IPLastByteError')

  return true
}

// 其他验证器
const requiredValidator = (value: string) => !!value || t('Device.AP.Required')

// 处理数据的函数提前声明
const dealData = (info: any, list: any[]) => {
  // 获取list中sn 的数据跟info一样的覆盖生成item
  console.log('list', list)
  let item = list.find((data: any) => data.sn === info.sn)
  if (!item) {
    item = info
  }
  else {
    item = {
      ...info,
      ...item,
    }
  }
  activeAP.value = item
  deviceInfoTable.value = [
    {
      label: t('Device.AP.DeviceTypeDesc'),
      value: 'AP',
    },
    {
      label: t('Device.AP.DeviceType'),
      value: item.model,
    },
    {
      label: t('Device.AP.SerialNumber'),
      value: item.sn,
    },
    {
      label: t('Device.AP.IPAddress'),
      value: item.ip,
    },
    {
      label: t('Device.AP.MACAddress'),
      value: item.mac,
    },
    {
      label: t('Device.AP.FirmwareVersion'),
      value: item.version || t('Device.AP.NoDataYet'),
    },
    {
      label: t('Device.AP.InterfaceRate'),
      value: `${item.speed} Mbps/s`,
    },
  ]
  if (item.wifiHtMode_2G == 'HT40') {
    if (item.wifiForce40MHzMode_2G == '0') {
      console.log('wirelessFormItems', 111)
      selectedBandwidth.value = '20/40M'
      HTMode.value = 'HT40'
    }
    else {
      console.log('wirelessFormItems', 222)
      selectedBandwidth.value = '40M'
      HTMode.value = 'HT40'
    }
  }
  else {
    console.log('wirelessFormItems', 333)
    selectedBandwidth.value = '20M'
    HTMode.value = 'HT20'
  }

  console.log('wirelessFormItems', item)
  wirelessFormItems.value = [
    {
      label: t('Device.AP.SSID'),
      value: item.ssid_2g,
      key: 'ssid',
      formType: 'input',
    },
    {
      label: t('Device.AP.EncryptionType'),
      value: item.encryption_2g,
      list: WIFI_ENCRYPTION_TYPE_LOCALIZED.value,
      key: 'encryption',
      formType: 'select',
    },
    {
      label: t('Device.AP.Password'),
      value: item.key_2g,
      key: 'password',
      formType: 'password',
      rules: [validatePasswordEight],
    },
    {
      label: t('Device.AP.Status'),
      value: item.wifiOnOff_2G === '0',
      key: 'status',
      formType: 'switch',
    },
    {
      label: t('Device.AP.APIsolation'),
      value: item.wifiApIsolate_2G === '1',
      key: 'apIsolation',
      formType: 'switch',
    },
    {
      label: t('Device.AP.Protocol'),
      value: item.wifiHwMode_2G,
      list: PROTOCOL_2G_LOCALIZED.value,
      key: 'protocol',
      formType: 'select',
    },
    {
      label: t('Device.AP.CountryCode'),
      value: item.wifiCountry_2G,
      list: COUNTRY_OPTIONS_LOCALIZED.value,
      key: 'countryCode',
      formType: 'select',
    },
    {
      label: t('Device.AP.Channel'),
      value: item.wifiChannel_2G,
      list: [],
      key: 'channel',
      formType: 'select',
    },
    {
      label: t('Device.AP.BandWidth'),
      value: selectedBandwidth.value,
      list: BAND_WIDTH_2G_LOCALIZED.value,
      key: 'bandwidth',
      formType: 'select',
      onChange: handleBandwidthChange,
    },
    {
      label: t('Device.AP.TransmitPower'),
      value: item.wifiTxpower_2G,
      list: TX_POWER_2G_LOCALIZED.value,
      key: 'transmitPower',
      formType: 'select',
    },
    {
      label: t('Device.AP.MaxConnections'),
      value: item.wifiMaxsta_2G,
      key: 'clientLimit',
      formType: 'input',
    },
  ]
  console.log(wirelessFormItems.value, item, 9999)

  // 初始化2.4G信道列表
  const index2G = COUNTRY_OPTIONS.findIndex((data: any) => data.value === item.wifiCountry_2G)
  if (index2G !== -1) {
    wirelessFormItems.value[7].list = CHANNEL_ARR_2G[index2G].map((channel: string) => ({
      label: channel,
      value: channel,
    }))
  }
  wirelessFormItems8G.value = [
    {
      label: t('Device.AP.SSID'),
      value: item.ssid_5g,
      key: 'ssid',
      formType: 'input',
    },
    {
      label: t('Device.AP.EncryptionType'),
      value: item.encryption_5g,
      list: WIFI_ENCRYPTION_TYPE_LOCALIZED.value,
      key: 'encryption',
      formType: 'select',
    },
    {
      label: t('Device.AP.Password'),
      value: item.key_5g,
      key: 'password',
      formType: 'password',
      rules: [validatePasswordEight],
    },
    {
      label: t('Device.AP.Status'),
      value: item.wifiOnOff_5G === '0',
      key: 'status',
      formType: 'switch',
    },
    {
      label: t('Device.AP.APIsolation'),
      value: item.wifiApIsolate_5G === '1',
      key: 'apIsolation',
      formType: 'switch',
    },
    {
      label: t('Device.AP.Protocol'),
      value: item.wifiHwMode_5G,
      list: PROTOCOL_5G,
      key: 'protocol',
      formType: 'select',
    },
    {
      label: t('Device.AP.CountryCode'),
      value: item.wifiCountry_5G,
      list: COUNTRY_OPTIONS_LOCALIZED.value,
      key: 'countryCode',
      formType: 'select',
    },
    {
      label: t('Device.AP.Channel'),
      value: item.wifiChannel_5G,
      list: [],
      key: 'channel',
      formType: 'select',
      onChange: changeChannel,
    },
    {
      label: t('Device.AP.BandWidth'),
      value: item.wifiHtMode_5G,
      list: BAND_WIDTH_5G_LOCALIZED.value,
      key: 'bandwidth',
      formType: 'select',
    },
    {
      label: t('Device.AP.TransmitPower'),
      value: item.wifiTxpower_5G,
      list: TX_POWER_5G_LOCALIZED,
      key: 'transmitPower',
      formType: 'select',
    },
    {
      label: t('Device.AP.MaxConnections'),
      value: item.wifiMaxsta_5G,
      key: 'clientLimit',
      formType: 'input',
    },
  ]

  // 初始化5G信道列表
  const index5G = COUNTRY_OPTIONS.findIndex((data: any) => data.value === item.wifiCountry_5G)
  if (index5G !== -1) {
    wirelessFormItems8G.value[7].list = CHANNEL_ARR_5G[index5G].map((channel: string) => ({
      label: channel,
      value: channel,
    }))
  }
  nameModeFormItems.value = [
    {
      label: t('Device.AP.DeviceName'),
      value: item.user_name,
      key: 'name',
      formType: 'input',
    },
  ]
  workModeFormItems.value = [
    {
      label: t('Device.AP.WorkingMode'),
      value: item.net_type,
      key: 'workMode',
      formType: 'select',
      list: NET_TYPE_LOCALIZED.value,
      subtitle: `${t('Device.AP.RouterModeAPModeDesc')}
${t('Device.AP.RouterModeDesc')}
${t('Device.AP.APModeDesc')}`,
    },
    {
      label: t('Device.AP.LANIP'),
      value: item.lan_ip,
      key: 'lan',
      formType: 'input',
      rules: [requiredValidator, validateLanIp],
    },
    {
      label: t('NetworkConfig.LAN.SubnetMask'),
      value: item.lan_netmask,
      formType: 'select',
      list: subnetMaskOptions,
      rules: [requiredValidator],
      key: 'subnetMask',
    },
    {
      label: t('Device.AP.LAN1'),
      value: item.eth1_link === 'up' ? t('Device.AP.Connected') : t('Device.AP.Disconnected'),
      key: 'lan1',
      formType: 'text',
    },
    {
      label: t('Device.AP.LAN2'),
      value: item.eth0_link === 'up' ? t('Device.AP.Connected') : t('Device.AP.Disconnected'),
      key: 'lan2',
      formType: 'text',
    },
  ]
  drawer.value = true
}

const restartDeviceFun = (item: any) => {
  if (!item.sn) {
    ElMessage.error(t('Device.AP.NoSN'))

    return
  }

  ElMessageBox.confirm(t('Device.AP.RestartConfirm'), t('Device.AP.Tip'), {
    confirmButtonText: t('Device.AP.Confirm'),
    cancelButtonText: t('Device.AP.Cancel'),
    type: 'warning',
  }).then(() => {
    $post('', {
      requestType: 501,
      data: {
        ap_sn_list: `${item.sn} `,
      },
    }).then(res => {
      if (res.msg === 'success')
        ElMessage.success(t('Device.AP.RestartSuccess'))
      dealRequestData()
    })
  }).catch(() => {
    ElMessage.info(t('Device.AP.RestartCancelled'))
  })
}

const delDeviceFun = (item: any) => {
  if (!item.sn) {
    ElMessage.error(t('Device.AP.NoSN'))

    return
  }
  console.log('item', item)
  ElMessageBox.confirm(t('Device.AP.DeleteConfirm'), t('Device.AP.Tip'), {
    confirmButtonText: t('Device.AP.Confirm'),
    cancelButtonText: t('Device.AP.Cancel'),
    type: 'warning',
  }).then(() => {
    $post('', {
      requestType: 508,
      data: {
        ap_sn_list: `${item.sn} `,
      },
    }).then(res => {
      if (res.msg === 'success') {
        ElMessage.success(t('Device.AP.DeleteSuccess'))

        // 删除成功后刷新列表数据
        setTimeout(() => {
          getApList()
        }, 1000)
      }
      else {
        ElMessage.error(res.msg || t('Device.AP.DeleteFailed'))
      }
    })
  }).catch(() => {
    ElMessage.info(t('Device.AP.DeleteCancelled'))
  })
}

const getApList = () => {
  const params: any = {
    dType: 0, // AP设备类型
    page: page.value,
    size: itemsPerPage.value,
  }

  // 只有当不是“全部”且不为 null 时才加 model 参数
  if (selectedModel.value && selectedModel.value !== '')
    params.model = selectedModel.value

  $get('/v1/device/list', params).then(res => {
    if (res.msg === 'success') {
      apData.value.total = res.result.count || 0

      // 为每个AP设备添加runstatus字段，默认值为-1
      apData.value.apList = (res.result.rows || [])
        .filter((item: any) => item.sn)
        .map((item: any) => ({
          ...item,
          runStatus: -1,
        })) || []

      // 选中逻辑：
      if (selectedModel.value === '') {
        // 选择“全部”时，不选中任何表格项
        selectedRows.value = []
      }
      else if (selectedModel.value) {
        // 选择具体型号时，选中该型号的所有
        selectedRows.value = apData.value.apList.filter((item: any) => item.model === selectedModel.value).map((item: any) => item.sn)
      }
      else {
        // 初始不选中
        selectedRows.value = []
      }
    }
  })
}

// 添加分页参数监听
watch([page, itemsPerPage], () => {
  getApList()
}, { immediate: false })

onMounted(() => {
  getModelList() // 页面初始化时获取型号列表
  getApList()
})

const getAPStatus = (bulkId: string, type: string) => {
  setTimeout(() => {
    $get(`/v1/bulkCmdResult/${bulkId}`, {}).then(res => {
      if (!res.result || !Array.isArray(res.result.rows))
        return
      const statusList = res.result.rows

      console.log('statusList', res)

      for (const item of statusList) {
        const sn = item.sn

        // result: true/false, online: true/false, data: string
        let newStatus = 0
        if (item.result === true)
          newStatus = 1 // 成功

        else
          newStatus = 2 // 失败或超时

        // 根据SN更新表格数组中对应项的runstatus和online
        const apIndex = apData.value.apList.findIndex((ap: any) => ap.sn === sn)
        if (apIndex !== -1) {
          apData.value.apList[apIndex].runStatus = newStatus
          apData.value.apList[apIndex].online = item.online
          apData.value.apList[apIndex].resultData = item.data
        }
      }
    })
  }, 3000)
}

// 执行
const execute = () => {
  if (operation.value === '') {
    ElMessage.error(t('Device.AP.SelectOperation'))

    return
  }
  if (selectedRows.value.length === 0) {
    ElMessage.error(t('Device.AP.SelectAP'))

    return
  }

  // 对于需要model参数的操作（如升级），检查型号一致性
  let currentModel = selectedModel.value
  if (!currentModel && operation.value === 2) {
    // 如果没有选择型号，检查选中设备的型号是否一致
    const selectedDevices = apData.value.apList.filter((item: any) =>
      selectedRows.value.includes(item.sn),
    )

    const models = [...new Set(selectedDevices.map((item: any) => item.model))]

    if (models.length > 1) {
      ElMessage.error(t('Tip.errorStr'))

      return
    }
    else if (models.length === 1) {
      currentModel = models[0]
    }
  }
  const selectStr = `${selectedRows.value.join(' ')} ` || ''
  switch (operation.value) {
  case 0:
    // 重启
    $post('/v1/bulkCmd', {
      deviceSns: selectedRows.value,
      cmd: 'restart',
    }).then(res => {
      if (res.msg === 'success') {
        getAPStatus(res.result)
        ElMessage.success(t('Device.AP.RestartSuccess'))
      }
    })
    break
  case 1:
    // 恢复出厂设置
    $post('/v1/bulkCmd', {
      deviceSns: selectedRows.value,
      cmd: 'factoryReset',
    }).then(res => {
      if (res.msg === 'success')
        ElMessage.success(t('Device.AP.ExecuteSuccess'))
    })
    break
  case 2:
    // 升级
    const file = fileList.value[0]
    const reader = new FileReader()

    reader.onload = (event: any) => {
      const backupFileData = event.target.result.split(',')
      const fileData = backupFileData[1]

      $post('', {
        requestType: 503,
        data: {
          model: currentModel,
          ap_sn_list: selectStr,
          fileName: fileList.value[0].name,
          fileSize: fileList.value[0].size.toString(),
          reset: resetAfterUpdate.value,
          file: fileData,
        },
      }).then(data => {
          if (data.err_code == 0) {
          getAPStatus(data.result)
          ElMessage.success(t('Device.AP.UpgradeSuccess'))
        }
      })
    }
    reader.readAsDataURL(file.raw)
    break
  case 3:
    $post('', {
      requestType: 508,
      data: {
        ap_sn_list: selectStr,
      },
    }).then(res => {
      if (res.msg === 'success') {
        ElMessage.success(t('Device.AP.DeleteSuccess'))

          // 批量删除成功后刷新列表数据
          setTimeout(() => {
          getApList()
        }, 1000)

        // 不再清空选中的行
        selectedRows.value = []
      }
      else {
        ElMessage.error(res.msg || t('Device.AP.DeleteFailed'))
      }
    })
    break
  case 4:
    // 闪烁LED
    $post('/v1/bulkCmd', {
      deviceSns: selectedRows.value,
      cmd: ledMode.value === '1' ? 'ledON' : 'ledOFF',
    }).then(res => {
      if (res.msg === 'success') {
          getAPStatus(res.result)
          ElMessage.success(t('Device.AP.ExecuteSuccess'))
        }
    })
    break
  case 5:
    // 导出 SN和MAC地址
    try {
      // 根据selectedRows获取对应的AP设备信息
      const selectedDevices = apData.value.apList.filter((item: any) =>
        selectedRows.value.includes(item.sn),
      )

      if (selectedDevices.length === 0) {
        ElMessage.warning('没有选中的设备')
        return
      }

      // 组成新数组，包含MAC、SN和设备名称
      const exportData = selectedDevices.map((device: any) => ({
        mac: device.mac || '--',
        sn: device.sn || '--',
        name: device.name || '--',
      }))

      // 生成txt文件内容，不添加表头
      const content = exportData.map(item => `${item.mac}\t${item.sn}\t${item.name}`).join('\n')
      const fileContent = content

      // 创建Blob对象
      const blob = new Blob([fileContent], { type: 'text/plain;charset=utf-8' })

      // 创建下载链接
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')

      link.href = url
      link.download = `AP设备信息_${new Date().toISOString().slice(0, 10)}.txt`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      // 释放URL对象
      URL.revokeObjectURL(url)

      ElMessage.success(`成功导出 ${selectedDevices.length} 个设备信息`)
    }
    catch (error) {
      console.error('导出设备信息失败:', error)
      ElMessage.error('导出设备信息失败')
    }
    break
  default:
    break
  }
}

const downloadFile = (url: string, fileName: string) => {
  const link = document.createElement('a')

  link.href = url
  link.download = fileName
  link.target = '_blank'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

const openLinkInNewWindow = (url: string) => {
  if (!url)
    return

  window.open(`http://${url}`, '_blank', 'noopener,noreferrer')
}

// 侧边栏
const wirelessType = ref('2.4G')

const wirelessTypeList = ref([
  {
    label: '2.4G',
    value: '2.4G',
  },
  {
    label: '5G',
    value: '5G',
  },
])

const showCurrentPassword = ref(false)

const roamingParameterFormItems = ref([{
  label: '快速漫游协议（802.11k/v/r）',
  value: '',
  key: 'roaming',
  formType: 'switch',
}, {
  label: '弱信号自动断开阈值',
  value: '',
  key: 'weakSignal',
  formType: 'input',
  subtitle: '推荐范围：-80 ~ -70 dBm',
}, {
  label: 'SSID间隔负载均衡间隔',
  value: '',
  key: 'ssidInterval',
  formType: 'select',
}, {
  label: '忽略弱信号探测',
  value: '',
  key: 'ignoreWeakSignal',
  formType: 'input',
  subtitle: '推荐范围：-90 ~ -80 dBm',
}, {
  label: '忽略过度重传终端',
  value: '',
  key: 'ignoreOverload',
  formType: 'select',
}])

const saveConfig = () => {
  console.log('saveConfig', activeAP.value)
  if (currentTab.value === 0) {
    $post('', {
      requestType: 513,
      data: {
        name: nameModeFormItems.value[0].value,
        ap_sn_list: `${activeAP.value.sn} `,
      },
    }).then(res => {
      if (res.msg === 'success') {
        ElMessage.success(t('Config.Mode.SaveSuccess'))
        drawer.value = false
        getApList()
      }
      else {
        ElMessage.error(res.msg || t('Device.AP.ModeSwitchFailed'))
      }
    })
  }

  if (currentTab.value === 1) {
    if (wirelessFormItems.value[1].value == 'none')
      wirelessFormItems.value[2].value = ''

    if (wirelessFormItems8G.value[1].value == 'none')
      wirelessFormItems8G.value[2].value = ''
    console.log('wirelessFormItems', HTMode.value, selectedBandwidth.value, wifiForce40MHzMode_2G.value)

    let wifiWpa3_2G = '0'
    let wifiWpa3_5G = '0'
    if (wirelessFormItems.value[1].value == 'psk2')
      wifiWpa3_2G = '1'

    if (wirelessFormItems8G.value[1].value == 'psk2')
      wifiWpa3_5G = '1'

    $post('', {
      requestType: 504,
      data: {
        ap_sn_list: `${activeAP.value.sn} `,
        wifiWpa3_2G,
        wifiWpa3_5G,
        ssid_2g: wirelessFormItems.value[0].value,
        ssid_5g: wirelessFormItems8G.value[0].value,
        wifiAuthmode_2G: wirelessFormItems.value[1].value,
        wifiAuthmode_5G: wirelessFormItems8G.value[1].value,
        key_2g: wirelessFormItems.value[2].value,
        key_5g: wirelessFormItems8G.value[2].value,
        wifiOnOff_2G: wirelessFormItems.value[3].value ? '0' : '1',
        wifiOnOff_5G: wirelessFormItems8G.value[3].value ? '0' : '1',
        wifiApIsolate_2G: wirelessFormItems.value[4].value ? '1' : '0',
        wifiApIsolate_5G: wirelessFormItems8G.value[4].value ? '1' : '0',
        wifiHwMode_2G: wirelessFormItems.value[5].value,
        wifiHwMode_5G: wirelessFormItems8G.value[5].value,
        wifiCountry_2G: wirelessFormItems.value[6].value,
        wifiCountry_5G: wirelessFormItems8G.value[6].value,
        wifiChannel_2G: wirelessFormItems.value[7].value,
        wifiChannel_5G: wirelessFormItems8G.value[7].value,
        wifiHtMode_2G: HTMode.value,
        wifiHtMode_5G: wirelessFormItems8G.value[8].value,
        wifiTxpower_2G: wirelessFormItems.value[9].value,
        wifiTxpower_5G: wirelessFormItems8G.value[9].value,
        wifiMaxsta_2G: wirelessFormItems.value[10].value || '',
        wifiMaxsta_5G: wirelessFormItems8G.value[10].value || '',
        wifiForce40MHzMode_2G: wifiForce40MHzMode_2G.value,
      },
    }).then(res => {
      if (res.msg === 'success')
        dealRequestData()
      else
        ElMessage.error(res.msg || t('Device.AP.ModeSwitchFailed'))
    })
  }
  if (currentTab.value === 2) {
    // 验证LAN IP格式
    const lanIpValue = workModeFormItems.value[1].value
    const ipValidationResult = validateLanIp(lanIpValue)

    if (ipValidationResult !== true) {
      ElMessage.error(ipValidationResult)

      return
    }

    $post('', {
      requestType: 504,
      data: {
        net_type: workModeFormItems.value[0].value,
        ap_sn_list: `${activeAP.value.sn} `,
        ap_lan_ip: workModeFormItems.value[1].value,
        ap_lan_mask: workModeFormItems.value[2].value,
      },
    }).then(res => {
      if (res.msg === 'success')
        dealRequestData()
      else
        ElMessage.error(res.msg || t('Device.AP.ModeSwitchFailed'))
    })
  }
  if (currentTab.value === 3) { /* empty */ }
}

const dealRequestData = () => {
  $post('', {
    requestType: 507,
    data: {},
  }).then(res => {
    if (res.msg === 'success') {
      //  获取设置的数组
      const statusList = res.info.apstatus.filter((item: any) => item.status && item.status !== '0')

      //  当前位置只有一个可以直接处理第一个就可以
      const status = Number(statusList[0].status)

      switch (status) {
      case 0:
        // 继续轮询
        setTimeout(() => {
          dealRequestData()
        }, 1000)
        break
      case 1:
        // 继续轮询
        setTimeout(() => {
          dealRequestData()
        }, 1000)
        break
      case 3:
        // 继续轮询
        setTimeout(() => {
          dealRequestData()
        }, 1000)
        break
      case 2:
        // 请求成功，关闭抽屉
        drawer.value = false
        ElMessage.success(t('Device.AP.NoConfigNeeded'))
        break
      case 4:
        // 请求成功，关闭抽屉
        drawer.value = false
        ElMessage.success(t('Device.AP.ConfigSuccess'))
        break
      case 99:
        // 异常结束
        ElMessage.error(t('Device.AP.VerificationTimeout'))
        drawer.value = false
        break
      case 999:
        // 请求超时
        ElMessage.error(t('Device.AP.DeployTimeout'))
        drawer.value = false
        break
      default:
        // 其他错误情况
        ElMessage.error(t('Device.AP.UnknownError'))
        drawer.value = false
        break
      }
    }
    else {
      ElMessage.error(res.msg || t('Device.AP.GetStatusFailed'))
      drawer.value = false
    }
    getApList()
  })
}

const switchNetwork = (rate: number) => {
  if (!rate)
    return '0 B/s'

  // 确保 rate 是数字类型
  const rateNum = Number(rate)
  if (isNaN(rateNum))
    return '0 B/s'

  if (rateNum < 1024)
    return `${rateNum.toFixed(2)} B/s`

  else if (rateNum < 1024 * 1024)
    return `${(rateNum / 1024).toFixed(2)} KB/s`

  else if (rateNum < 1024 * 1024 * 1024)
    return `${(rateNum / (1024 * 1024)).toFixed(2)} MB/s`

  else
    return `${(rateNum / (1024 * 1024 * 1024)).toFixed(2)} GB/s`
}

const wifiForce40MHzMode_2G = ref('0')

// 处理带宽选择变化的函数
const handleBandwidthChange = value => {
  console.log('bandwidth changed:', value)
  selectedBandwidth.value = value

  // 根据选择的带宽值更新HTMode和wifiForce40MHzMode_2G
  switch (value) {
  case '40M':
    HTMode.value = 'HT40'
    wifiForce40MHzMode_2G.value = '1'
    break
  case '20/40M':
    HTMode.value = 'HT40'
    wifiForce40MHzMode_2G.value = '0'
    break
  case '20M':
  default:
    HTMode.value = 'HT20'
    wifiForce40MHzMode_2G.value = '0'
    break
  }
}

const validatePasswordEight = (value: string) => {
  // 如果加密类型为none，则不验证密码
  if (wirelessFormItems.value[1].value === 'none')
    return true

  // 验证密码长度是否至少为8位
  if (!value || value.length < 8)
    return t('Device.AP.EnterEightDigitPassword')

  return true
}

// 新增：根据5G信道动态计算可用的带宽选项
const BAND_WIDTH_5G_LOCALIZED = computed(() => {
  const channel = wirelessFormItems8G.value[7]?.value

  console.log('channel', channel)

  // 默认情况或自动信道：提供所有带宽选项
  if (!channel || channel === 'auto') {
    return [
      { label: '20M', value: 'HT20' },
      { label: '40M', value: 'HT40' },
      { label: '80M', value: 'HT80' },
      { label: '160M', value: 'HT160' },
    ]
  }

  const channelNum = Number.parseInt(channel, 10)

  // 根据信道范围返回相应的带宽选项
  if (channelNum >= 36 && channelNum <= 128) {
    // 36-128 可选20, 40, 80, 160
    return [
      { label: '20M', value: 'HT20' },
      { label: '40M', value: 'HT40' },
      { label: '80M', value: 'HT80' },
      { label: '160M', value: 'HT160' },
    ]
  }
  else if (channelNum === 132 || channelNum === 136) {
    // 132、136 可选20, 40
    return [
      { label: '20M', value: 'HT20' },
      { label: '40M', value: 'HT40' },
    ]
  }
  else if (channelNum === 140 || channelNum === 144) {
    // 140、144只能选20
    return [
      { label: '20M', value: 'HT20' },
    ]
  }
  else if (channelNum >= 149 && channelNum <= 161) {
    // 149-161 可选 20, 40, 80
    return [
      { label: '20M', value: 'HT20' },
      { label: '40M', value: 'HT40' },
      { label: '80M', value: 'HT80' },
    ]
  }
  else {
    // 161以上可选 20
    return [
      { label: '20M', value: 'HT20' },
    ]
  }
})

const changeChannel = () => {
  const newBandWidthOptions = BAND_WIDTH_5G_LOCALIZED.value
  const currentValue = wirelessFormItems8G.value[8].value
  const isValidOption = newBandWidthOptions.some(option => option.value === currentValue)

  if (!isValidOption && newBandWidthOptions.length > 0) {
    // 如果当前选择不可用，则默认选择列表中第一个选项
    wirelessFormItems8G.value[8].value = newBandWidthOptions[0].value
  }
}

// 排序事件
const sortchange = (val: any) => {
  sortBy.value = val
}

// 监听selectedModel变化，切换型号时重新获取数据并处理选中逻辑
watch(selectedModel, () => {
  page.value = 1 // 重置到第一页
  getApList()
})
</script>

<template>
  <div class="ap-manager">
    <VCard class="mb-6">
      <div class="d-flex flex-wrap gap-4 ma-6">
        <div class="d-flex align-center">
          <div class="cardTitle">
            {{ t('Device.AP.Title') }}
          </div>
        </div>
        <VSpacer />
      </div>
      <VDivider class="mt-4" />
      <div class="pa-6 d-flex">
        <AppSelect
          v-model="selectedModel"
          :items="modelList"
          class="mr-4"
          item-title="label"
          item-value="value"
          :placeholder="t('Config.AP.PleaseSelectModel')"
        />
        <AppSelect
          v-model="operation"
          :items="apHandlers"
          class="mr-4"
          item-title="label"
          item-value="value"
          :placeholder="t('Device.AP.SelectOperation')"
          @update:model-value="operationChange"
        />
        <!-- 升级 -->
        <div
          v-if="operation === 2"
          class="d-flex align-center mr-4"
        >
          <ElUpload
            ref="uploader"
            :auto-upload="false"
            :limit="1"
            :multiple="false"
            :on-change="handleChange"
            :on-exceed="handleExceed"
            :show-file-list="false"
            class="mr-4"
          >
            <span class="mr-4 text-secondary">{{
              fileList && fileList.length ? fileList[0].name : t('Device.AP.NoFileSelected')
            }}</span>
            <VBtn
              color="primary"
              variant="tonal"
            >
              {{ t('Device.AP.SelectFile') }}
            </VBtn>
          </ElUpload>
          <VCheckbox
            v-model="resetAfterUpdate"
            false-value="0"
            :label="t('Device.AP.RestoreFactory')"
            true-value="1"
          />
        </div>
        <!-- led -->
        <div
          v-if="operation === 4"
          class="mr-4"
        >
          <VCheckbox
            v-model="ledMode"
            :label="ledMode === '1' ? t('Device.AP.Blink') : t('Device.AP.Normal')"
            false-value="0"
            true-value="1"
          />
        </div>
        <VBtn @click="execute">
          <VIcon icon="tabler-checks" />
          {{ t('Device.AP.Confirm') }}
        </VBtn>
      </div>
      <VDivider />
      <VDataTable
        v-model="selectedRows"
        v-model:items-per-page="itemsPerPage"
        v-model:page="page"
        :headers="headers"
        :items="apData.apList"
        :items-length="apData.total"
        class="text-no-wrap"
        item-value="sn"
        show-select
        select-strategy="page"
        :no-data-text="t('NoData')"
        :sort-by="sortBy"
        @update:sort-by="sortchange"
      >
        <template #item.name="{ item }">
          <div>{{ item.name || '--' }}</div>
        </template>
        <template #item.ip="{ item }">
          <div
            class="text-primary cursor-pointer hover-underline"
            @click="openLinkInNewWindow(item.deviceInfo.wan_ip)"
          >
            {{
              item.deviceInfo.wan_ip
                || '--'
            }}
          </div>
        </template>
        <template #item.mac="{ item }">
          {{ item.mac || '--' }}
        </template>
        <template #item.onoff="{ item }">
          <template v-if="item.runStatus == -1">
            <VChip
              v-if="item.online"
              label
              color="success"
              size="small"
            >
              {{ t('Device.AP.Online') }}
            </VChip>
            <VChip
              v-else
              label
              color="error"
              size="small"
            >
              {{ t('Device.AP.Offline') }}
            </VChip>
          </template>
          <template v-else>
            <span v-if="item.runStatus == 1">
              <img
                width="30"
                height="30"
                src="@images/success.png"
                alt=""
              >
            </span>
            <span v-if="item.runStatus == 2">{{ t('TimeOut') }}</span>
          </template>
        </template>
        <template #item.uptime="{ item }">
          {{ item.runtime || '--' }}
        </template>
        <template #item.actions="{ item }">
          <IconBtn>
            <VIcon icon="tabler-dots-vertical" />
            <VMenu activator="parent">
              <VList>
                <VListItem @click="remoteConfigFun(item)">
                  {{ t('Device.AP.RemoteConfig') }}
                </VListItem>

                <VListItem @click="deviceManagerFun(item)">
                  {{ t('Device.AP.DeviceManagement') }}
                </VListItem>

                <VListItem @click="restartDeviceFun(item)">
                  {{ t('Device.AP.RestartDevice') }}
                </VListItem>

                <VListItem @click="delDeviceFun(item)">
                  {{ t('Device.AP.DeleteDevice') }}
                </VListItem>
              </VList>
            </VMenu>
          </IconBtn>
        </template>

        <template #bottom>
          <TablePagination
            v-model:page="page"
            :items-per-page="itemsPerPage"
            :total-items="apData.total"
          />
        </template>
      </VDataTable>
    </VCard>

    <VNavigationDrawer
      v-if="drawer"
      v-model="drawer"
      location="right"
      temporary
      persistent
      width="560"
    >
      <div class="h-screen d-flex flex-column">
        <!-- 顶部固定区域 -->
        <div class="flex-shrink-0 d-flex align-center justify-space-between pa-4">
          <div class="text-h5">
            {{ t('Device.AP.DeviceManagement') }}
          </div>
          <VBtn
            color="medium-emphasis"
            icon
            size="small"
            variant="text"
            @click="drawer = false"
          >
            <VIcon
              color="high-emphasis"
              icon="tabler-x"
              size="24"
            />
          </VBtn>
        </div>

        <VDivider />

        <!-- 中间可滚动区域 -->
        <div class="flex-grow-1 overflow-hidden">
          <PerfectScrollbar
            :options="{ wheelPropagation: false }"
            class="h-100"
            tag="div"
          >
            <div class="pa-4 d-flex flex-column h-100">
              <div class="flex-shrink-0">
                <div class="d-flex mb-6">
                  <div
                    class="rounded mr-4"
                    style="block-size: 66px;inline-size: 66px;"
                  >
                    <svg
                      width="66"
                      height="66"
                      viewBox="0 0 66 66"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M54.6616 1H11.3384C5.62867 1 1 5.62867 1 11.3384V54.6616C1 60.3713 5.62867 65 11.3384 65H54.6616C60.3713 65 65 60.3713 65 54.6616V11.3384C65 5.62867 60.3713 1 54.6616 1Z"
                        fill="url(#paint0_linear_1247_23640)"
                      />
                      <path
                        d="M54.6616 1H11.3384C5.62867 1 1 5.62867 1 11.3384V54.6616C1 60.3713 5.62867 65 11.3384 65H54.6616C60.3713 65 65 60.3713 65 54.6616V11.3384C65 5.62867 60.3713 1 54.6616 1Z"
                        stroke="url(#paint1_linear_1247_23640)"
                        stroke-width="1.64103"
                      />
                      <path
                        d="M17.6836 25.3459V40.6622C17.6836 44.8916 21.1123 48.3203 25.3417 48.3203H40.6581C44.8875 48.3203 48.3161 44.8916 48.3161 40.6622V25.3459C48.3161 21.1164 44.8875 17.6877 40.6581 17.6877H25.3417C21.1123 17.6877 17.6836 21.1164 17.6836 25.3459Z"
                        fill="#3773F5"
                        stroke="#3773F5"
                        stroke-width="2.05128"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M36.8267 25.3457H29.168"
                        stroke="white"
                        stroke-width="2.05128"
                        stroke-linejoin="round"
                      />
                      <defs>
                        <linearGradient
                          id="paint0_linear_1247_23640"
                          x1="33"
                          y1="0.179487"
                          x2="33"
                          y2="65.8205"
                          gradientUnits="userSpaceOnUse"
                        >
                          <stop stop-color="#F0F5FF" />
                          <stop
                            offset="1"
                            stop-color="#EFF4FF"
                          />
                        </linearGradient>
                        <linearGradient
                          id="paint1_linear_1247_23640"
                          x1="33"
                          y1="0.179487"
                          x2="33"
                          y2="64.1795"
                          gradientUnits="userSpaceOnUse"
                        >
                          <stop
                            stop-color="#E1EBFF"
                            stop-opacity="0"
                          />
                          <stop
                            offset="1"
                            stop-color="#DEE9FF"
                          />
                        </linearGradient>
                      </defs>
                    </svg>
                  </div>
                  <div class="d-flex flex-column justify-space-around">
                    <div class="d-flex align-center">
                      <span class="text-h5">{{ activeAP.user_name || '--' }}</span>
                      <VChip
                        v-if="activeAP.onoff === 'online'"
                        class="ml-2"
                        color="success"
                        size="small"
                      >
                        {{ t('Device.AP.Online') }}
                      </VChip>
                      <VChip
                        v-else
                        class="ml-2"
                        color="error"
                        size="small"
                      >
                        {{ t('Device.AP.Offline') }}
                      </VChip>
                    </div>
                    <div class="d-flex align-center text-subtitle-1">
                      <div class="text-primary mr-2">
                        <VIcon
                          icon="tabler-arrow-narrow-down"
                          size="x-small"
                        />
                        {{ switchNetwork(activeAP.tx_rate) }}
                      </div>
                      <div class="text-success">
                        <VIcon
                          icon="tabler-arrow-narrow-up"
                          size="x-small"
                        />
                        {{ switchNetwork(activeAP.rx_rate) }}
                      </div>
                    </div>
                  </div>
                </div>

                <VTabs v-model="currentTab">
                  <VTab :value="0">
                    {{ t('Device.AP.DeviceInfo') }}
                  </VTab>
                  <VTab :value="1">
                    {{ t('Device.AP.WirelessStatus') }}
                  </VTab>
                  <VTab :value="2">
                    {{ t('Device.AP.WorkMode') }}
                  </VTab>
                </VTabs>
              </div>

              <div class="flex-grow-1 overflow-hidden mt-4">
                <VWindow
                  v-model="currentTab"
                  class="h-100 d-flex flex-column"
                >
                  <VWindowItem
                    :value="0"
                    class="flex-grow-1 overflow-auto"
                    style=" -ms-overflow-style: none;scrollbar-width: none;"
                  >
                    <div class="bg-grey-light pa-4 my-4 rounded">
                      <VRow>
                        <VCol>
                          <div class="label">
                            {{ t('Device.AP.InternetStatus') }}
                          </div>
                          <div
                            v-if="activeAP.internet === '0'"
                            class="value text-success"
                          >
                            {{ t('Device.AP.Online') }}
                          </div>
                          <div
                            v-else
                            class="value text-error"
                          >
                            {{ t('Device.AP.Offline') }}
                          </div>
                        </VCol>
                        <VCol>
                          <div class="label">
                            {{ t('Device.AP.RunningTime') }}
                          </div>
                          <div class="value">
                            {{ activeAP.runtime }}
                          </div>
                        </VCol>
                        <VCol>
                          <div class="label">
                            {{ t('Device.AP.WorkingMode') }}
                          </div>
                          <div class="value">
                            {{ activeAP.net_type === '0' ? t('Device.AP.RouterMode') : t('Device.AP.APMode') }}
                          </div>
                        </VCol>
                      </VRow>
                    </div>
                    <VForm>
                      <VForm v-if="wirelessType === '2.4G'">
                        <div
                          v-for="(item, index) in nameModeFormItems"
                          :key="index"
                          class="d-flex align-start mb-4"
                        >
                          <div class="w-80 h-38 flex-0-0 mr-4 text-subtitle-2">
                            {{ item.label }}
                          </div>
                          <div class="w-100">
                            <AppTextField
                              v-if="item.formType === 'input'"
                              v-model="item.value"
                              :rules="item.rules"
                              append-inner-icon="tabler-edit"
                            />
                            <div
                              v-if="item.subtitle"
                              class="text-subtitle-2"
                            >
                              {{ item.subtitle }}
                            </div>
                          </div>
                        </div>
                      </VForm>
                    </VForm>
                    <div>
                      <div
                        v-for="(item, index) in deviceInfoTable"
                        :key="index"
                        class="d-flex h-38 mb-4"
                      >
                        <div class="flex-0-0 mr-4 w-80 text-on-surface opacity-90 text-subtitle-2">
                          {{ item.label }}
                        </div>
                        <div class="flex-1-0 text-secondary opacity-70 text-subtitle-1">
                          {{ item.value }}
                        </div>
                      </div>
                    </div>
                  </VWindowItem>
                  <VWindowItem
                    :value="1"
                    class="flex-grow-1 overflow-auto"
                    style=" -ms-overflow-style: none;scrollbar-width: none;"
                  >
                    <VForm ref="formRef2">
                      <!-- SSID配置类型选择按钮组 -->
                      <div class="d-flex justify-space-between align-center mb-4">
                        <BtnGroupSelector
                          v-model:value="templateForm.ssidConfigType"
                          fill-row
                          :options="ssidConfigTypeList"
                          @update:value="ssidConfigTypeChange"
                        />
                      </div>

                      <!-- 双频合一开关 (仅默认配置显示) -->
                      <div
                        v-if="templateForm.ssidConfigType === '0'"
                        class="d-flex justify-space-between align-center mb-4"
                      >
                        <div class="text-subtitle-2 text-on-surface opacity-90">
                          {{ t('Config.AP.DualBandUnify') }}
                        </div>
                        <div class="d-flex align-center">
                          <VSwitch
                            v-model="templateForm.ssid_type"
                            class="mr-2"
                            false-value="0"
                            true-value="1"
                            @update:model-value="ssidTypeChange"
                          />
                          <span class="text-subtitle-2 text-on-surface opacity-50">
                            {{ t('Config.AP.DualBandUnifyHint') }}
                          </span>
                        </div>
                      </div>
                      <!-- 默认配置 - 双频合一 -->
                      <div v-if="templateForm.ssidConfigType === '0' && templateForm.ssid_type === '1'">
                        <!-- 双频合一SSID名称输入 -->
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.SSID') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.ssid"
                            :rules="[(v: string) => requiredValidator(v, t('Config.Mode.EnterSSID'))]"
                            :placeholder="t('Config.Mode.EnterSSID')"
                          />
                        </div>
                        <!-- 双频合一加密类型选择 -->
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.EncryptionType') }}
                          </div>
                          <AppSelect
                            v-model="templateForm.encryptionType"
                            :items="ENCRYPTION_TYPE_LOCALIZED"
                            :placeholder="t('Config.Mode.SelectEncryption')"
                            :rules="[(v: string) => requiredValidator(v, t('Config.Mode.SelectEncryption'))]"
                            item-title="label"
                            item-value="value"
                          />
                        </div>
                        <!-- 双频合一密码输入 -->
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.Password') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.password"
                            :append-inner-icon="showMergePassword ? 'tabler-eye-off' : 'tabler-eye'"
                            :placeholder="t('Config.Mode.EnterPassword')"
                            :rules="[validatePassword]"
                            :type="showMergePassword ? 'text' : 'password'"
                            @click:append-inner="
                              showMergePassword = !showMergePassword
                            "
                          />
                        </div>
                        <!-- 双频合一客户端隔离开关 -->
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.Isolate') }}
                          </div>
                          <div class="d-flex align-center">
                            <VSwitch
                              v-model="templateForm.isolate"
                              class="mr-2"
                            />
                            <span class="text-subtitle-2 text-on-surface opacity-50">
                              {{ templateForm.isolate ? t('Config.Mode.On') : t('Config.Mode.Off') }}
                            </span>
                          </div>
                        </div>
                      </div>
                      <VDivider class="mb-4" />
                      <div class="d-flex justify-space-between align-center mb-4">
                        <div class="text-primary text-h5">
                          {{ t('Config.Mode.WirelessSettings2G') }}
                        </div>
                        <VBtn
                          v-if="templateForm.ssidConfigType === '1' && ssid2gCount < 2"
                          color="primary"
                          size="small"
                          variant="outlined"
                          @click="addSSID2G"
                        >
                          {{ t('Config.Mode.AddSSID') }}
                        </VBtn>
                      </div>

                      <!-- 默认配置 - 2.4G分开模式 -->
                      <div v-if="templateForm.ssidConfigType === '0' && templateForm.ssid_type === '0'">
                        <!-- 启用Wi-Fi开关 -->
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.EnableWiFi') }}
                          </div>
                          <VSwitch
                            v-model="templateForm.wifiOnOff_2G"
                            false-value="1"
                            true-value="0"
                            :label="templateForm.wifiOnOff_2G == '0' ? t('Config.Mode.On') : t('Config.Mode.Off')"
                          />
                        </div>
                        <!-- SSID名称输入 -->
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.SSID') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.ssid_2g"
                            :rules="[(v: string) => requiredValidatorNew(v, t('Config.Mode.EnterSSID'))]"
                            :placeholder="t('Config.Mode.EnterSSID')"
                          />
                        </div>
                        <!-- 2.4G加密类型选择 -->
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.EncryptionType') }}
                          </div>
                          <AppSelect
                            v-model="templateForm.wifiAuthmode_2G"
                            :items="ENCRYPTION_TYPE_LOCALIZED"
                            item-title="label"
                            item-value="value"
                            :placeholder="t('Config.Mode.SelectEncryption')"
                            :rules="[(v: string) => requiredValidator(v, t('Config.Mode.SelectEncryption'))]"
                          />
                        </div>
                        <!-- 2.4G密码输入 -->
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.Password') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.key_2g"
                            :placeholder="t('Config.Mode.EnterPassword')"
                            :rules="[validatePasswordEight]"
                            :append-inner-icon="show2GPassword ? 'tabler-eye-off' : 'tabler-eye'"
                            :type="show2GPassword ? 'text' : 'password'"
                            @click:append-inner="show2GPassword = !show2GPassword"
                          />
                        </div>
                      </div>

                      <!-- 多SSID配置 - 2.4G WiFi开关 -->
                      <div v-if="templateForm.ssidConfigType === '1'">
                        <!-- 2.4G WiFi总开关 -->
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.EnableWiFi') }}
                          </div>
                          <VSwitch
                            v-model="templateForm.wifiOnOff_2G"
                            false-value="1"
                            true-value="0"
                            :label="templateForm.wifiOnOff_2G == '0' ? t('Config.Mode.On') : t('Config.Mode.Off')"
                          />
                        </div>
                      </div>

                      <!-- 通用2.4G设置 -->
                      <!-- 2.4G协议选择 -->
                      <div class="d-flex justify-space-between align-start mb-4">
                        <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                          {{ t('Config.Mode.Protocol') }}
                        </div>
                        <AppSelect
                          v-model="templateForm.wifiHwMode_2G"
                          :items="PROTOCOL_2G_LOCALIZED"
                          :rules="[(v: string) => {
                            if (v === null) {
                              return t('Config.Mode.SelectProtocol')
                            }
                          }]"
                          item-title="label"
                          item-value="value"
                          :placeholder="t('Config.Mode.SelectProtocol')"
                        />
                      </div>
                      <!-- 2.4G国家码选择 -->
                      <div class="d-flex justify-space-between align-start mb-4">
                        <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                          {{ t('Config.Mode.Country') }}
                        </div>
                        <AppSelect
                          v-model="templateForm.wifiCountry_2G"
                          :items="COUNTRY_OPTIONS_LOCALIZED"
                          :rules="[(v: string) => {
                            if (v === null) {
                              return t('Config.Mode.SelectCountry')
                            }
                          }]"
                          item-title="label"
                          item-value="value"
                          :placeholder="t('Config.Mode.SelectCountry')"
                          @update:model-value="() => {
                            templateForm.wifiChannel_2G = 'auto'
                          }"
                        />
                      </div>
                      <!-- 2.4G信道选择 -->
                      <div class="d-flex justify-space-between align-start mb-4">
                        <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                          {{ t('Config.Mode.Channel') }}
                        </div>
                        <AppSelect
                          v-model="templateForm.wifiChannel_2G"
                          :items="channelOptions2g"
                          :rules="[(v: string) => {
                            if (v === null) {
                              return t('Config.Mode.SelectChannel')
                            }
                          }]"
                          :placeholder="t('Config.Mode.SelectChannel')"
                        />
                      </div>
                      <!-- 2.4G信道带宽选择 -->
                      <div class="d-flex justify-space-between align-start mb-4">
                        <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                          {{ t('Config.Mode.Bandwidth') }}
                        </div>
                        <AppSelect
                          v-model="selectedBandwidth"
                          :items="BAND_WIDTH_2G_LOCALIZED"
                          :rules="[(v: string) => {
                            if (v === null) {
                              return t('Config.Mode.SelectBandwidth')
                            }
                          }]"
                          item-title="label"
                          item-value="value"
                          :placeholder="t('Config.Mode.SelectBandwidth')"
                        />
                      </div>
                      <!-- 2.4G发射功率选择 -->
                      <div class="d-flex justify-space-between align-start mb-4">
                        <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                          {{ t('Config.Mode.TxPower') }}
                        </div>
                        <AppSelect
                          v-model="templateForm.wifiTxpower_2G"
                          :items="TX_POWER_2G_LOCALIZED"
                          :rules="[(v: string) => {
                            if (!v && v !== '') {
                              return t('Config.Mode.SelectTxPower')
                            }
                          }]"
                          item-title="label"
                          item-value="value"
                          :placeholder="t('Config.Mode.SelectTxPower')"
                        />
                      </div>
                      <!-- 2.4G最大连接数输入 -->
                      <div class="d-flex justify-space-between align-start mb-4">
                        <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                          {{ t('Device.AP.MaxConnections') }}
                        </div>
                        <AppTextField v-model="templateForm.wifiMaxsta_2G" />
                      </div>

                      <!-- 多SSID配置卡片 - 2.4G -->
                      <div v-if="templateForm.ssidConfigType === '1'">
                        <!-- 2.4G SSID 1 -->
                        <VCard
                          class="mb-4"
                          variant="outlined"
                        >
                          <VCardTitle class="d-flex justify-space-between align-center">
                            <span>{{ t('Config.Mode.SSID2G1') }}</span>
                          </VCardTitle>
                          <VCardText>
                            <div class="d-flex justify-space-between align-start mb-4">
                              <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                                {{ t('Config.Mode.SSID') }}
                              </div>
                              <AppTextField
                                v-model="templateForm.ssid_2g"
                                :rules="[(v: string) => requiredValidator(v, t('Config.Mode.EnterSSID'))]"
                                :placeholder="t('Config.Mode.EnterSSID')"
                              />
                            </div>
                            <div class="d-flex justify-space-between align-start mb-4">
                              <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                                {{ t('Config.Mode.Password') }}
                              </div>
                              <AppTextField
                                v-model="templateForm.key_2g"
                                :placeholder="t('Config.Mode.EnterPassword')"
                                :rules="[validatePasswordEightMore]"
                                :append-inner-icon="show2GPassword ? 'tabler-eye-off' : 'tabler-eye'"
                                :type="show2GPassword ? 'text' : 'password'"
                                @click:append-inner="show2GPassword = !show2GPassword"
                              />
                            </div>
                            <div class="d-flex justify-space-between align-start mb-4">
                              <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                                {{ t('Config.Mode.VLANID') }}
                              </div>
                              <AppSelect
                                v-model="templateForm.vlanTemplateId24G1"
                                :items="vlanList"
                                item-title="itemTitle"
                                item-value="id"
                                :placeholder="t('Config.Mode.SelectVLAN')"
                                @update:model-value="(value: any) => handleVlanSelect('vlanId24G1', value)"
                              />
                            </div>
                            <div class="d-flex justify-space-between align-start mb-4">
                              <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                                {{ t('Config.Mode.MaxConnections') }}
                              </div>
                              <AppTextField
                                v-model="templateForm.wifiMaxsta_2G"
                                :placeholder="t('Config.Mode.EnterMaxConnections')"
                                type="number"
                              />
                            </div>
                            <div class="d-flex justify-space-between align-start mb-4">
                              <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                                {{ t('Config.Mode.APIsolation') }}
                              </div>
                              <VSwitch
                                v-model="templateForm.wifiApIsolate_2G"
                                false-value="0"
                                true-value="1"
                                :label="templateForm.wifiApIsolate_2G === '1' ? t('Config.Mode.On') : t('Config.Mode.Off')"
                              />
                            </div>
                          </VCardText>
                        </VCard>

                        <!-- 2.4G SSID 2 -->
                        <VCard
                          v-if="ssid2gCount >= 2"
                          class="mb-4"
                          variant="outlined"
                        >
                          <VCardTitle class="d-flex justify-space-between align-center">
                            <span>{{ t('Config.Mode.SSID2G2') }}</span>
                            <VBtn
                              color="error"
                              size="small"
                              variant="text"
                              icon="tabler-trash"
                              @click="removeSSID2G"
                            />
                          </VCardTitle>
                          <VCardText>
                            <div class="d-flex justify-space-between align-start mb-4">
                              <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                                {{ t('Config.Mode.SSID') }}
                              </div>
                              <AppTextField
                                v-model="templateForm.ssid_2g2"
                                :rules="[(v: string) => requiredValidator(v, t('Config.Mode.EnterSSID'))]"
                                :placeholder="t('Config.Mode.EnterSSID')"
                              />
                            </div>
                            <div class="d-flex justify-space-between align-start mb-4">
                              <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                                {{ t('Config.Mode.Password') }}
                              </div>
                              <AppTextField
                                v-model="templateForm.key_2g2"
                                :placeholder="t('Config.Mode.EnterPassword')"
                                :rules="[validatePasswordEightMore]"
                                :append-inner-icon="show2GPassword ? 'tabler-eye-off' : 'tabler-eye'"
                                :type="show2GPassword ? 'text' : 'password'"
                                @click:append-inner="show2GPassword = !show2GPassword"
                              />
                            </div>
                            <div class="d-flex justify-space-between align-start mb-4">
                              <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                                {{ t('Config.Mode.VLANID') }}
                              </div>
                              <AppSelect
                                v-model="templateForm.vlanTemplateId24G2"
                                :items="vlanList"
                                item-title="itemTitle"
                                item-value="id"
                                :placeholder="t('Config.Mode.SelectVLAN')"
                                @update:model-value="(value: any) => handleVlanSelect('vlanId24G2', value)"
                              />
                            </div>
                            <div class="d-flex justify-space-between align-start mb-4">
                              <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                                {{ t('Config.Mode.MaxConnections') }}
                              </div>
                              <AppTextField
                                v-model="templateForm.wifiMaxsta_2G2"
                                :placeholder="t('Config.Mode.EnterMaxConnections')"
                                type="number"
                              />
                            </div>
                            <div class="d-flex justify-space-between align-start mb-4">
                              <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                                {{ t('Config.Mode.APIsolation') }}
                              </div>
                              <VSwitch
                                v-model="templateForm.wifiApIsolate_2G2"
                                false-value="0"
                                true-value="1"
                                :label="templateForm.wifiApIsolate_2G2 === '1' ? t('Config.Mode.On') : t('Config.Mode.Off')"
                              />
                            </div>
                          </VCardText>
                        </VCard>
                      </div>
                      <!-- 2.4G客户端隔离开关（仅默认配置分开模式显示） -->
                      <div
                        v-if="templateForm.ssidConfigType === '0' && templateForm.ssid_type === '0'"
                        class="d-flex justify-space-between align-start mb-4"
                      >
                        <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                          {{ t('Config.Mode.Isolate') }}
                        </div>
                        <VSwitch
                          v-model="templateForm.wifiApIsolate_2G"
                          false-value="0"
                          true-value="1"
                          :label="templateForm.wifiApIsolate_2G === '0' ? t('Config.Mode.Off') : t('Config.Mode.On')"
                        />
                      </div>
                      <VDivider class="mb-4" />
                      <!-- 5G -->
                      <div class="d-flex justify-space-between align-center mb-4">
                        <div class="text-primary text-h5">
                          {{ t('Config.Mode.WirelessSettings5G') }}
                        </div>
                        <VBtn
                          v-if="templateForm.ssidConfigType === '1' && ssid5gCount < 2"
                          color="primary"
                          size="small"
                          variant="outlined"
                          @click="addSSID5G"
                        >
                          {{ t('Config.Mode.AddSSID') }}
                        </VBtn>
                      </div>

                      <!-- 默认配置 - 5G分开模式 -->
                      <div v-if="templateForm.ssidConfigType === '0' && templateForm.ssid_type === '0'">
                        <!-- 5G WiFi开关 -->
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.EnableWiFi') }}
                          </div>
                          <div class="d-flex align-center">
                            <VSwitch
                              v-model="templateForm.wifiOnOff_5G"
                              class="mr-2"
                              false-value="1"
                              true-value="0"
                            />
                            <span class="text-subtitle-2 text-on-surface opacity-50">
                              {{
                                templateForm.wifiOnOff_5G === "0" ? t('Config.Mode.On') : t('Config.Mode.Off')
                              }}
                            </span>
                          </div>
                        </div>
                        <!-- 5G SSID名称输入 -->
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.SSID') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.ssid_5g"
                            :rules="[(v: string) => requiredValidatorNew(v, t('Config.Mode.EnterSSID'))]"
                            :placeholder="t('Config.Mode.EnterSSID')"
                          />
                        </div>
                        <!-- 5G加密类型选择 -->
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.EncryptionType') }}
                          </div>
                          <AppSelect
                            v-model="templateForm.wifiAuthmode_5G"
                            :items="ENCRYPTION_TYPE_LOCALIZED"
                            :rules="templateForm.ssid_type === '0' ? [(v: string) => {
                              if (v === null) {
                                return t('Config.Mode.SelectEncryption')
                              }
                            }] : []"
                            item-title="label"
                            item-value="value"
                            :placeholder="t('Config.Mode.SelectEncryption')"
                          />
                        </div>
                        <!-- 5G密码输入 -->
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.Password') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.key_5g"
                            :append-inner-icon="show5GPassword ? 'tabler-eye-off' : 'tabler-eye'"
                            :rules="[validatePasswordEight5G]"
                            :type="show5GPassword ? 'text' : 'password'"
                            :placeholder="t('Config.Mode.EnterPassword')"
                            @click:append-inner="show5GPassword = !show5GPassword"
                          />
                        </div>
                      </div>

                      <!-- 多SSID配置 - 5G WiFi开关 -->
                      <div v-if="templateForm.ssidConfigType === '1'">
                        <!-- 5G WiFi总开关 -->
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.EnableWiFi') }}
                          </div>
                          <VSwitch
                            v-model="templateForm.wifiOnOff_5G"
                            false-value="1"
                            true-value="0"
                            :label="templateForm.wifiOnOff_5G == '0' ? t('Config.Mode.On') : t('Config.Mode.Off')"
                          />
                        </div>
                      </div>

                      <!-- 通用5G设置 -->
                      <!-- 5G协议选择 -->
                      <div class="d-flex justify-space-between align-start mb-4">
                        <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                          {{ t('Config.Mode.Protocol') }}
                        </div>
                        <AppSelect
                          v-model="templateForm.wifiHwMode_5G"
                          :items="PROTOCOL_5G_LOCALIZED"
                          :rules="[(v: string) => {
                            if (v === null) {
                              return t('Config.Mode.SelectProtocol')
                            }
                          }]"
                          item-title="label"
                          item-value="value"
                          :placeholder="t('Config.Mode.SelectProtocol')"
                        />
                      </div>
                      <!-- 5G国家码选择 -->
                      <div class="d-flex justify-space-between align-start mb-4">
                        <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                          {{ t('Config.Mode.Country') }}
                        </div>
                        <AppSelect
                          v-model="templateForm.wifiCountry_5G"
                          :items="COUNTRY_OPTIONS_LOCALIZED"
                          :rules="[(v: string) => {
                            if (v === null) {
                              return t('Config.Mode.SelectCountry')
                            }
                          }]"
                          item-title="label"
                          item-value="value"
                          :placeholder="t('Config.Mode.SelectCountry')"
                          @update:model-value="() => {
                            templateForm.wifiChannel_5G = 'auto'
                          }"
                        />
                      </div>
                      <!-- 5G信道选择 -->
                      <div class="d-flex justify-space-between align-start mb-4">
                        <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                          {{ t('Config.Mode.Channel') }}
                        </div>
                        <AppSelect
                          v-model="templateForm.wifiChannel_5G"
                          :items="channelOptions5g"
                          :rules="[(v: string) => {
                            if (v === null) {
                              return t('Config.Mode.SelectChannel')
                            }
                          }]"
                          :placeholder="t('Config.Mode.SelectChannel')"
                          @update:model-value="changeChannel"
                        />
                      </div>
                      <!-- 5G信道带宽选择 -->
                      <div class="d-flex justify-space-between align-start mb-4">
                        <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                          {{ t('Config.Mode.Bandwidth') }}
                        </div>
                        <AppSelect
                          v-model="templateForm.wifiHtMode_5G"
                          :items="BAND_WIDTH_5G_LOCALIZED"
                          :rules="[(v: string) => {
                            if (v === null) {
                              return t('Config.Mode.SelectBandwidth')
                            }
                          }]"
                          item-title="label"
                          item-value="value"
                          :placeholder="t('Config.Mode.SelectBandwidth')"
                        />
                      </div>
                      <!-- 5G发射功率选择 -->
                      <div class="d-flex justify-space-between align-start mb-4">
                        <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                          {{ t('Config.Mode.TxPower') }}
                        </div>
                        <AppSelect
                          v-model="templateForm.wifiTxpower_5G"
                          :items="TX_POWER_5G_LOCALIZED"
                          :rules="[(v: string) => {
                            if (!v && v !== '') {
                              return t('Config.Mode.SelectTxPower')
                            }
                          }]"
                          item-title="label"
                          item-value="value"
                          :placeholder="t('Config.Mode.SelectTxPower')"
                        />
                      </div>
                      <!-- 5G最大连接数输入 -->
                      <div class="d-flex justify-space-between align-start mb-4">
                        <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                          {{ t('Device.AP.MaxConnections') }}
                        </div>
                        <AppTextField v-model="templateForm.wifiMaxsta_5G" />
                      </div>

                      <!-- 多SSID配置卡片 - 5G -->
                      <div v-if="templateForm.ssidConfigType === '1'">
                        <!-- 5G SSID 1 -->
                        <VCard
                          class="mb-4"
                          variant="outlined"
                        >
                          <VCardTitle class="d-flex justify-space-between align-center">
                            <span>{{ t('Config.Mode.SSID5G1') }}</span>
                          </VCardTitle>
                          <VCardText>
                            <div class="d-flex justify-space-between align-start mb-4">
                              <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                                {{ t('Config.Mode.SSID') }}
                              </div>
                              <AppTextField
                                v-model="templateForm.ssid_5g"
                                :rules="[(v: string) => requiredValidator(v, t('Config.Mode.EnterSSID'))]"
                                :placeholder="t('Config.Mode.EnterSSID')"
                              />
                            </div>
                            <div class="d-flex justify-space-between align-start mb-4">
                              <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                                {{ t('Config.Mode.Password') }}
                              </div>
                              <AppTextField
                                v-model="templateForm.key_5g"
                                :placeholder="t('Config.Mode.EnterPassword')"
                                :rules="[validatePasswordEight5GMore]"
                                :append-inner-icon="show5GPassword ? 'tabler-eye-off' : 'tabler-eye'"
                                :type="show5GPassword ? 'text' : 'password'"
                                @click:append-inner="show5GPassword = !show5GPassword"
                              />
                            </div>
                            <div class="d-flex justify-space-between align-start mb-4">
                              <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                                {{ t('Config.Mode.VLANID') }}
                              </div>
                              <AppSelect
                                v-model="templateForm.vlanTemplateId5G1"
                                :items="vlanList"
                                item-title="itemTitle"
                                item-value="id"
                                :placeholder="t('Config.Mode.SelectVLAN')"
                                @update:model-value="(value: any) => handleVlanSelect('vlanId5G1', value)"
                              />
                            </div>
                            <div class="d-flex justify-space-between align-start mb-4">
                              <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                                {{ t('Config.Mode.MaxConnections') }}
                              </div>
                              <AppTextField
                                v-model="templateForm.wifiMaxsta_5G"
                                :placeholder="t('Config.Mode.EnterMaxConnections')"
                                type="number"
                              />
                            </div>
                            <div class="d-flex justify-space-between align-start mb-4">
                              <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                                {{ t('Config.Mode.APIsolation') }}
                              </div>
                              <VSwitch
                                v-model="templateForm.wifiApIsolate_5G"
                                false-value="0"
                                true-value="1"
                                :label="templateForm.wifiApIsolate_5G === '1' ? t('Config.Mode.On') : t('Config.Mode.Off')"
                              />
                            </div>
                          </VCardText>
                        </VCard>

                        <!-- 5G SSID 2 -->
                        <VCard
                          v-if="ssid5gCount >= 2"
                          class="mb-4"
                          variant="outlined"
                        >
                          <VCardTitle class="d-flex justify-space-between align-center">
                            <span>{{ t('Config.Mode.SSID5G2') }}</span>
                            <VBtn
                              color="error"
                              size="small"
                              variant="text"
                              icon="tabler-trash"
                              @click="removeSSID5G"
                            />
                          </VCardTitle>
                          <VCardText>
                            <div class="d-flex justify-space-between align-start mb-4">
                              <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                                {{ t('Config.Mode.SSID') }}
                              </div>
                              <AppTextField
                                v-model="templateForm.ssid_5g2"
                                :rules="[(v: string) => requiredValidator(v, t('Config.Mode.EnterSSID'))]"
                                :placeholder="t('Config.Mode.EnterSSID')"
                              />
                            </div>
                            <div class="d-flex justify-space-between align-start mb-4">
                              <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                                {{ t('Config.Mode.Password') }}
                              </div>
                              <AppTextField
                                v-model="templateForm.key_5g2"
                                :placeholder="t('Config.Mode.EnterPassword')"
                                :rules="[validatePasswordEight5GMore]"
                                :append-inner-icon="show5GPassword ? 'tabler-eye-off' : 'tabler-eye'"
                                :type="show5GPassword ? 'text' : 'password'"
                                @click:append-inner="show5GPassword = !show5GPassword"
                              />
                            </div>
                            <div class="d-flex justify-space-between align-start mb-4">
                              <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                                {{ t('Config.Mode.VLANID') }}
                              </div>
                              <AppSelect
                                v-model="templateForm.vlanTemplateId5G2"
                                :items="vlanList"
                                item-title="itemTitle"
                                item-value="id"
                                :placeholder="t('Config.Mode.SelectVLAN')"
                                @update:model-value="(value: any) => handleVlanSelect('vlanId5G2', value)"
                              />
                            </div>
                            <div class="d-flex justify-space-between align-start mb-4">
                              <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                                {{ t('Config.Mode.MaxConnections') }}
                              </div>
                              <AppTextField
                                v-model="templateForm.wifiMaxsta_5G2"
                                :placeholder="t('Config.Mode.EnterMaxConnections')"
                                type="number"
                              />
                            </div>
                            <div class="d-flex justify-space-between align-start mb-4">
                              <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                                {{ t('Config.Mode.APIsolation') }}
                              </div>
                              <VSwitch
                                v-model="templateForm.wifiApIsolate_5G2"
                                false-value="0"
                                true-value="1"
                                :label="templateForm.wifiApIsolate_5G2 === '1' ? t('Config.Mode.On') : t('Config.Mode.Off')"
                              />
                            </div>
                          </VCardText>
                        </VCard>
                      </div>

                      <!-- 5G客户端隔离开关（仅默认配置分开模式显示） -->
                      <div
                        v-if="templateForm.ssidConfigType === '0' && templateForm.ssid_type == '0'"
                        class="d-flex justify-space-between align-start mb-4"
                      >
                        <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                          {{ t('Config.Mode.Isolate') }}
                        </div>
                        <div class="d-flex align-center">
                          <VSwitch
                            v-model="templateForm.wifiApIsolate_5G"
                            class="mr-2"
                            false-value="0"
                            true-value="1"
                          />
                          <span class="text-subtitle-2 text-on-surface opacity-50">
                            {{
                              templateForm.wifiApIsolate_5G === "0" ? t('Config.Mode.Off') : t('Config.Mode.On')
                            }}
                          </span>
                        </div>
                      </div>
                    </VForm>
                  </VWindowItem>
                  <VWindowItem
                    :value="2"
                    class="flex-grow-1 overflow-auto"
                    style=" -ms-overflow-style: none;scrollbar-width: none;"
                  >
                    <VForm>
                      <div
                        v-for="(item, index) in workModeFormItems"
                        :key="index"
                        class="d-flex align-start mt-4"
                      >
                        <div class="w-80 h-38 flex-0-0 mr-4 text-subtitle-2">
                          {{ item.label }}
                        </div>
                        <div class="w-100">
                          <AppTextField
                            v-if="item.formType === 'input'"
                            v-model="item.value"
                            :rules="item.rules"
                            append-inner-icon="tabler-edit"
                          />
                          <AppSelect
                            v-else-if="item.formType === 'select'"
                            v-model="item.value"
                            :items="item.list"
                            :rules="item.rules"
                            item-title="label"
                          />
                          <AppTextField
                            v-else-if="item.formType === 'password'"
                            v-model="item.value"
                            :append-inner-icon="showCurrentPassword ? 'tabler-eye-off' : 'tabler-eye'"
                            :rules="[requiredValidator]"
                            :type="showCurrentPassword ? 'text' : 'password'"
                            @click:append-inner="showCurrentPassword = !showCurrentPassword"
                          />
                          <div
                            v-else-if="item.formType === 'switch'"
                            class="d-flex align-center justify-end"
                          >
                            <VSwitch
                              v-model="item.value"
                              class="mr-2"
                            />
                            <span class="text-subtitle-2">{{ item.value ? t('Device.AP.Enable') : t('Device.AP.Disable') }}</span>
                          </div>
                          <div
                            v-else-if="item.formType === 'text'"
                            class="h-38"
                          >
                            {{ item.value }}
                          </div>
                          <div
                            v-if="item.subtitle"
                            class="text-subtitle-2"
                          >
                            {{ item.subtitle }}
                          </div>
                        </div>
                      </div>
                    </VForm>
                  </VWindowItem>
                </VWindow>
              </div>
            </div>
          </PerfectScrollbar>
        </div>

        <!-- 底部固定区域 -->
        <VDivider />
        <div class="flex-shrink-0 pa-4 d-flex align-center justify-space-between">
          <VBtn
            color="primary"
            variant="outlined"
            @click="remoteConfigFun(activeAP)"
          >
            {{ t('Device.AP.RemoteConfig') }}
          </VBtn>
          <div>
            <VBtn
              class="mr-4"
              color="secondary"
              variant="tonal"
              @click="drawer = false"
            >
              {{ t('Device.AP.Cancel') }}
            </VBtn>
            <VBtn
              color="primary"
              @click="saveConfig"
            >
              {{ t('Device.AP.Save') }}
            </VBtn>
          </div>
        </div>
      </div>
    </VNavigationDrawer>
  </div>
</template>

<style lang="scss" scoped>
.flexBox {
  display: flex;
  align-items: center;
}

.cardTitle {
  font-family: "PingFang SC", serif;
  font-size: 18px;
  font-weight: 600;
  line-height: 28px;
}

.ap-manager {
  .label {
    color: #999;
    font-size: 13px;
    margin-block-end: 2px;
  }

  .value {
    color: rgba($color: var(--v-theme-on-surface), $alpha: 90%);
    font-size: 15px;
  }

  .sub-title {
    color: rgba($color: var(--v-theme-on-surface), $alpha: 55%);
    font-size: 15px;
    font-weight: normal;
  }

  .rounded-progress {
    border-end-start-radius: 10px !important;
    border-start-start-radius: 10px !important;
  }

  .h-38 {
    block-size: 38px;
    line-height: 38px;
  }

  .w-80 {
    inline-size: 80px;
  }
}
</style>
